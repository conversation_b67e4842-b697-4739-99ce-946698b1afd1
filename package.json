{"name": "back-end", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.12.7", "@strapi/plugin-documentation": "^5.12.7", "@strapi/plugin-users-permissions": "5.12.7", "@strapi/provider-email-nodemailer": "^5.19.0", "@strapi/provider-email-sendgrid": "^5.19.0", "@strapi/strapi": "5.12.7", "better-sqlite3": "11.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "f9800306de6d4c2d59bf08436e27b9cabd892194fdf3e4db900b00eccad27cce"}}