'use strict';

/**
 * Email controller
 */

export default {
  /**
   * Send reservation confirmation email manually
   */
  async sendReservationConfirmation(ctx) {
    try {
      const { reservationId } = ctx.params;
      
      if (!reservationId) {
        return ctx.badRequest('Reservation ID is required');
      }

      const emailService = strapi.service('api::email.email-notification');
      const result = await emailService.sendReservationConfirmation(reservationId);

      ctx.send({
        message: 'Reservation confirmation email sent successfully',
        data: result,
      });

    } catch (error) {
      console.error('Error in sendReservationConfirmation controller:', error);
      ctx.badRequest(error.message);
    }
  },

  /**
   * Send payment confirmation email manually
   */
  async sendPaymentConfirmation(ctx) {
    try {
      const { reservationId } = ctx.params;
      
      if (!reservationId) {
        return ctx.badRequest('Reservation ID is required');
      }

      const emailService = strapi.service('api::email.email-notification');
      const result = await emailService.sendPaymentConfirmation(reservationId);

      ctx.send({
        message: 'Payment confirmation email sent successfully',
        data: result,
      });

    } catch (error) {
      console.error('Error in sendPaymentConfirmation controller:', error);
      ctx.badRequest(error.message);
    }
  },

  /**
   * Send test email
   */
  async sendTestEmail(ctx) {
    try {
      const { email } = ctx.request.body;
      
      if (!email) {
        return ctx.badRequest('Email address is required');
      }

      const emailService = strapi.service('api::email.email-notification');
      const result = await emailService.sendTestEmail(email);

      ctx.send({
        message: 'Test email sent successfully',
        data: result,
      });

    } catch (error) {
      console.error('Error in sendTestEmail controller:', error);
      ctx.badRequest(error.message);
    }
  },

  /**
   * Test email configuration
   */
  async testEmailConfig(ctx) {
    try {
      // Try to send a test email using Strapi's email service
      await strapi.plugins['email'].services.email.send({
        to: process.env.EMAIL_TEST_ADDRESS || '<EMAIL>',
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: 'Email Configuration Test',
        text: 'This is a test email to verify email configuration.',
        html: '<p>This is a test email to verify email configuration.</p>',
      });

      ctx.send({
        message: 'Email configuration test successful',
        config: {
          defaultFrom: process.env.EMAIL_DEFAULT_FROM,
          testAddress: process.env.EMAIL_TEST_ADDRESS,
        },
      });

    } catch (error) {
      console.error('Error in testEmailConfig controller:', error);
      ctx.badRequest(`Email configuration test failed: ${error.message}`);
    }
  },
};
