'use strict';

/**
 * Email router
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/email/reservation-confirmation/:reservationId',
      handler: 'api::email.email.sendReservationConfirmation',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/email/payment-confirmation/:reservationId',
      handler: 'api::email.email.sendPaymentConfirmation',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/email/test',
      handler: 'api::email.email.sendTestEmail',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/email/test-config',
      handler: 'api::email.email.testEmailConfig',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
