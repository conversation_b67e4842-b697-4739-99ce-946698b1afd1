'use strict';

/**
 * Email notification service
 */

export default () => ({
  /**
   * Send reservation confirmation email
   */
  async sendReservationConfirmation(reservationId) {
    try {
      // Get the full reservation data with populated relations
      const reservation = await strapi.documents('api::reservation.reservation').findOne({
        documentId: reservationId,
        populate: {
          user: true,
          event: true,
        },
      });

      if (!reservation) {
        throw new Error('Reservation not found');
      }

      // Check if user and event data are available
      if (!reservation.user || !reservation.event) {
        throw new Error('Missing user or event data');
      }

      // Get user email
      const userEmail = reservation.user.email;
      if (!userEmail) {
        throw new Error('User email not found');
      }

      // Format event date and time
      const eventDate = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : 'TBD';

      const eventTime = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : 'TBD';

      // Prepare email template data
      const emailData = {
        customerName: reservation.user.username || reservation.user.firstName || 'Valued Customer',
        reservationId: reservation.documentId,
        eventName: reservation.event.name || 'Event',
        eventDate: eventDate,
        eventTime: eventTime,
        numberOfGuests: reservation.ticket_count || 1,
        totalPrice: reservation.total_price || 0,
      };

      // Get email template
      const emailTemplateService = strapi.service('api::email.email-templates');
      const emailTemplate = emailTemplateService.getReservationConfirmationTemplate(emailData);

      // Send email
      await strapi.plugins['email'].services.email.send({
        to: userEmail,
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: emailTemplate.subject,
        text: emailTemplate.text,
        html: emailTemplate.html,
      });

      console.log(`Reservation confirmation email sent to: ${userEmail}`);
      return { success: true, email: userEmail };

    } catch (error) {
      console.error('Error sending reservation confirmation email:', error);
      throw error;
    }
  },

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(reservationId) {
    try {
      // Get the full reservation data with populated relations
      const reservation = await strapi.documents('api::reservation.reservation').findOne({
        documentId: reservationId,
        populate: {
          user: true,
          event: true,
        },
      });

      if (!reservation) {
        throw new Error('Reservation not found');
      }

      // Check if user and event data are available
      if (!reservation.user || !reservation.event) {
        throw new Error('Missing user or event data');
      }

      // Get user email
      const userEmail = reservation.user.email;
      if (!userEmail) {
        throw new Error('User email not found');
      }

      // Format event date and time
      const eventDate = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : 'TBD';

      const eventTime = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : 'TBD';

      // Prepare email template data
      const emailData = {
        customerName: reservation.user.username || reservation.user.firstName || 'Valued Customer',
        reservationId: reservation.documentId,
        eventName: reservation.event.name || 'Event',
        eventDate: eventDate,
        eventTime: eventTime,
        numberOfGuests: reservation.ticket_count || 1,
        totalPrice: reservation.total_price || 0,
        paymentReference: reservation.payment_reference,
      };

      // Get email template
      const emailTemplateService = strapi.service('api::email.email-templates');
      const emailTemplate = emailTemplateService.getPaymentSuccessTemplate(emailData);

      // Send email
      await strapi.plugins['email'].services.email.send({
        to: userEmail,
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: emailTemplate.subject,
        text: emailTemplate.text,
        html: emailTemplate.html,
      });

      console.log(`Payment confirmation email sent to: ${userEmail}`);
      return { success: true, email: userEmail };

    } catch (error) {
      console.error('Error sending payment confirmation email:', error);
      throw error;
    }
  },

  /**
   * Send test email to verify email configuration
   */
  async sendTestEmail(toEmail) {
    try {
      const testEmailData = {
        customerName: 'Test Customer',
        reservationId: 'TEST-12345',
        eventName: 'Test Event',
        eventDate: 'Monday, January 1, 2024',
        eventTime: '7:00 PM',
        numberOfGuests: 2,
        totalPrice: 100,
      };

      const emailTemplateService = strapi.service('api::email.email-templates');
      const emailTemplate = emailTemplateService.getReservationConfirmationTemplate(testEmailData);

      await strapi.plugins['email'].services.email.send({
        to: toEmail,
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: '[TEST] ' + emailTemplate.subject,
        text: '[TEST EMAIL]\n\n' + emailTemplate.text,
        html: emailTemplate.html.replace('<h1>', '<h1>[TEST EMAIL] '),
      });

      console.log(`Test email sent to: ${toEmail}`);
      return { success: true, email: toEmail };

    } catch (error) {
      console.error('Error sending test email:', error);
      throw error;
    }
  },
});
