'use strict';

/**
 * Email templates service
 */

export default () => ({
  /**
   * Generate reservation confirmation email template
   */
  getReservationConfirmationTemplate(data) {
    const {
      customerName,
      reservationId,
      eventName,
      eventDate,
      eventTime,
      numberOfGuests,
      totalPrice,
      hotelName = process.env.HOTEL_NAME || 'Hotel Booking',
      hotelAddress = process.env.HOTEL_ADDRESS || '',
      hotelPhone = process.env.HOTEL_PHONE || '',
      hotelEmail = process.env.HOTEL_EMAIL || '',
      hotelWebsite = process.env.HOTEL_WEBSITE || '',
    } = data;

    const subject = `Reservation Confirmation - ${eventName}`;

    const text = `
Dear ${customerName},

Thank you for your reservation! We're excited to confirm your booking.

RESERVATION DETAILS:
- Reservation ID: ${reservationId}
- Event: ${eventName}
- Date: ${eventDate}
- Time: ${eventTime}
- Number of Guests: ${numberOfGuests}
- Total Price: $${totalPrice}

Your reservation is currently PENDING payment. Please complete your payment to confirm your booking.

If you have any questions, please don't hesitate to contact us:
- Phone: ${hotelPhone}
- Email: ${hotelEmail}
- Website: ${hotelWebsite}

Thank you for choosing ${hotelName}!

Best regards,
The ${hotelName} Team
    `.trim();

    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservation Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .reservation-details { background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; color: #2c3e50; }
        .status-pending { color: #e67e22; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        .contact-info { background-color: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 Reservation Confirmed!</h1>
        <p>Thank you for choosing ${hotelName}</p>
    </div>
    
    <div class="content">
        <p>Dear <strong>${customerName}</strong>,</p>
        
        <p>We're excited to confirm your reservation! Here are your booking details:</p>
        
        <div class="reservation-details">
            <h3>📋 Reservation Details</h3>
            <div class="detail-row">
                <span class="detail-label">Reservation ID:</span>
                <span>${reservationId}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Event:</span>
                <span>${eventName}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date:</span>
                <span>${eventDate}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Time:</span>
                <span>${eventTime}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Number of Guests:</span>
                <span>${numberOfGuests}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Total Price:</span>
                <span><strong>$${totalPrice}</strong></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="status-pending">PENDING PAYMENT</span>
            </div>
        </div>
        
        <p><strong>⚠️ Important:</strong> Your reservation is currently pending payment. Please complete your payment to confirm your booking.</p>
        
        <div class="contact-info">
            <h4>📞 Contact Information</h4>
            <p><strong>Phone:</strong> ${hotelPhone}<br>
            <strong>Email:</strong> ${hotelEmail}<br>
            <strong>Website:</strong> ${hotelWebsite}</p>
        </div>
        
        <p>If you have any questions or need to make changes to your reservation, please don't hesitate to contact us.</p>
        
        <p>We look forward to welcoming you!</p>
    </div>
    
    <div class="footer">
        <p>Best regards,<br>
        <strong>The ${hotelName} Team</strong></p>
        ${hotelAddress ? `<p><small>${hotelAddress}</small></p>` : ''}
    </div>
</body>
</html>
    `.trim();

    return { subject, text, html };
  },

  /**
   * Generate payment success email template
   */
  getPaymentSuccessTemplate(data) {
    const {
      customerName,
      reservationId,
      eventName,
      eventDate,
      eventTime,
      numberOfGuests,
      totalPrice,
      paymentReference,
      hotelName = process.env.HOTEL_NAME || 'Hotel Booking',
      hotelAddress = process.env.HOTEL_ADDRESS || '',
      hotelPhone = process.env.HOTEL_PHONE || '',
      hotelEmail = process.env.HOTEL_EMAIL || '',
      hotelWebsite = process.env.HOTEL_WEBSITE || '',
    } = data;

    const subject = `Payment Confirmed - ${eventName} Reservation`;

    const text = `
Dear ${customerName},

Great news! Your payment has been successfully processed and your reservation is now CONFIRMED.

CONFIRMED RESERVATION DETAILS:
- Reservation ID: ${reservationId}
- Event: ${eventName}
- Date: ${eventDate}
- Time: ${eventTime}
- Number of Guests: ${numberOfGuests}
- Total Price: $${totalPrice}
- Payment Reference: ${paymentReference || 'N/A'}

Your booking is now confirmed! We look forward to welcoming you.

WHAT'S NEXT:
- Save this confirmation email for your records
- Arrive 15 minutes before your event time
- Bring a valid ID for verification

If you need to make any changes or have questions, please contact us:
- Phone: ${hotelPhone}
- Email: ${hotelEmail}
- Website: ${hotelWebsite}

Thank you for choosing ${hotelName}!

Best regards,
The ${hotelName} Team
    `.trim();

    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmed</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #27ae60; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .reservation-details { background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #27ae60; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; color: #2c3e50; }
        .status-confirmed { color: #27ae60; font-weight: bold; }
        .next-steps { background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        .contact-info { background-color: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Payment Confirmed!</h1>
        <p>Your reservation is now confirmed</p>
    </div>
    
    <div class="content">
        <p>Dear <strong>${customerName}</strong>,</p>
        
        <p>🎉 <strong>Great news!</strong> Your payment has been successfully processed and your reservation is now <strong>CONFIRMED</strong>.</p>
        
        <div class="reservation-details">
            <h3>📋 Confirmed Reservation Details</h3>
            <div class="detail-row">
                <span class="detail-label">Reservation ID:</span>
                <span>${reservationId}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Event:</span>
                <span>${eventName}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date:</span>
                <span>${eventDate}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Time:</span>
                <span>${eventTime}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Number of Guests:</span>
                <span>${numberOfGuests}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Total Price:</span>
                <span><strong>$${totalPrice}</strong></span>
            </div>
            ${paymentReference ? `
            <div class="detail-row">
                <span class="detail-label">Payment Reference:</span>
                <span>${paymentReference}</span>
            </div>
            ` : ''}
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="status-confirmed">✅ CONFIRMED</span>
            </div>
        </div>
        
        <div class="next-steps">
            <h4>📝 What's Next?</h4>
            <ul>
                <li>Save this confirmation email for your records</li>
                <li>Arrive 15 minutes before your event time</li>
                <li>Bring a valid ID for verification</li>
                <li>Contact us if you need to make any changes</li>
            </ul>
        </div>
        
        <div class="contact-info">
            <h4>📞 Contact Information</h4>
            <p><strong>Phone:</strong> ${hotelPhone}<br>
            <strong>Email:</strong> ${hotelEmail}<br>
            <strong>Website:</strong> ${hotelWebsite}</p>
        </div>
        
        <p>We look forward to welcoming you and making your experience memorable!</p>
    </div>
    
    <div class="footer">
        <p>Best regards,<br>
        <strong>The ${hotelName} Team</strong></p>
        ${hotelAddress ? `<p><small>${hotelAddress}</small></p>` : ''}
    </div>
</body>
</html>
    `.trim();

    return { subject, text, html };
  },
});
