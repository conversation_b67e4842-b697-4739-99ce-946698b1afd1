{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Event", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string"}, "price": {"type": "biginteger"}, "description": {"type": "text"}, "date": {"type": "datetime"}, "icon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "images": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": true}, "max_attendees": {"type": "integer"}, "duration": {"type": "integer"}, "is_active": {"type": "boolean"}, "reservations": {"type": "relation", "relation": "oneToMany", "target": "api::reservation.reservation", "mappedBy": "event"}}}