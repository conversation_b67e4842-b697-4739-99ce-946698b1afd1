{"kind": "collectionType", "collectionName": "reservations", "info": {"singularName": "reservation", "pluralName": "reservations", "displayName": "Reservation", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"event": {"type": "relation", "relation": "manyToOne", "target": "api::event.event", "inversedBy": "reservations"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "reservations"}, "booking_date": {"type": "datetime"}, "statutR": {"type": "enumeration", "enum": ["pending", "confirmed", "cancelled"]}, "ticket_count": {"type": "integer"}, "total_price": {"type": "integer"}, "payment_reference": {"type": "string"}}}