<!-- HTML for static distribution bundle build --><!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="/plugins/documentation/swagger-ui.css">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-16x16.png" sizes="16x16">
    <style>
      html {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after {
        box-sizing: inherit;
      }

      body {
        margin: 0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script class="custom-swagger-ui">
      window.onload = function() {
        const ui = SwaggerUIBundle({
          url: "https://petstore.swagger.io/v2/swagger.json",
          spec: {"openapi":"3.0.0","info":{"version":"1.0.0","title":"DOCUMENTATION","description":"","termsOfService":"YOUR_TERMS_OF_SERVICE_URL","contact":{"name":"TEAM","email":"<EMAIL>","url":"mywebsite.io"},"license":{"name":"Apache 2.0","url":"https://www.apache.org/licenses/LICENSE-2.0.html"},"x-generation-date":"2025-05-05T19:29:29.557Z"},"x-strapi-config":{"plugins":["upload","users-permissions"]},"servers":[{"url":"http://localhost:1337/api","description":"Development server"}],"externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"},"security":[{"bearerAuth":[]}],"paths":{"/events":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/events"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[],"operationId":"post/events","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRequest"}}}}}},"/events/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/events/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/events/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/events/{id}"}},"/reservations":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Reservation"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/reservations"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Reservation"],"parameters":[],"operationId":"post/reservations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationRequest"}}}}}},"/reservations/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Reservation"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/reservations/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Reservation"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/reservations/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ReservationRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Reservation"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/reservations/{id}"}},"/upload":{"post":{"description":"Upload files","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"required":["files"],"type":"object","properties":{"path":{"type":"string","description":"The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."},"refId":{"type":"string","description":"The ID of the entry which the file(s) will be linked to"},"ref":{"type":"string","description":"The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."},"field":{"type":"string","description":"The field of the entry which the file(s) will be precisely linked to."},"files":{"type":"array","items":{"type":"string","format":"binary"}}}}}}}}},"/upload?id={id}":{"post":{"parameters":[{"name":"id","in":"query","description":"File id","required":true,"schema":{"type":"string"}}],"description":"Upload file information","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"fileInfo":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"}}},"files":{"type":"string","format":"binary"}}}}}}}},"/upload/files":{"get":{"tags":["Upload - File"],"responses":{"200":{"description":"Get a list of files","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}}}},"/upload/files/{id}":{"get":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Get a specific file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}},"delete":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Delete a file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}}},"/connect/{provider}":{"get":{"parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string","pattern":".*"}}],"tags":["Users-Permissions - Auth"],"summary":"Login with a provider","description":"Redirects to provider login before being redirect to /auth/{provider}/callback","responses":{"301":{"description":"Redirect response"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Local login","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"identifier":{"type":"string"},"password":{"type":"string"}}},"example":{"identifier":"foobar","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Connection","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local/register":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Register a user","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"username":{"type":"string"},"email":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foobar","email":"<EMAIL>","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Successful registration","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/{provider}/callback":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Default Callback from provider auth","parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string"}}],"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/forgot-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send rest password email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}},"example":{"email":"<EMAIL>"}}}},"responses":{"200":{"description":"Returns ok","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/reset-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Rest user password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"password":{"type":"string"},"passwordConfirmation":{"type":"string"},"code":{"type":"string"}}},"example":{"password":"Test1234","passwordConfirmation":"Test1234","code":"zertyoaizndoianzodianzdonaizdoinaozdnia"}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/change-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Update user's own password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["password","currentPassword","passwordConfirmation"],"properties":{"password":{"type":"string"},"currentPassword":{"type":"string"},"passwordConfirmation":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/email-confirmation":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Confirm user email","parameters":[{"in":"query","name":"confirmation","schema":{"type":"string"},"description":"confirmation token received by email"}],"responses":{"301":{"description":"Redirects to the configure email confirmation redirect url"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/send-email-confirmation":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send confirmation email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns email and boolean to confirm email was sent","content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"},"sent":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/permissions":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get default generated permissions","responses":{"200":{"description":"Returns the permissions tree","content":{"application/json":{"schema":{"type":"object","properties":{"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}},"controllerB":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"List roles","responses":{"200":{"description":"Returns list of roles","content":{"application/json":{"schema":{"type":"object","properties":{"roles":{"type":"array","items":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-Role"},{"type":"object","properties":{"nb_users":{"type":"number"}}}]}}}},"example":{"roles":[{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","nb_users":0}]}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a role","requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was create","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a role","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns the role","content":{"application/json":{"schema":{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}},"example":{"role":{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{role}":{"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was udpated","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns ok if the role was delete","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get list of users","responses":{"200":{"description":"Returns an array of users","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Users-Permissions-User"}},"example":[{"id":9,"username":"<EMAIL>","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-06-01T18:32:35.211Z","updatedAt":"2022-06-01T18:32:35.217Z"}]}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a user","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"201":{"description":"Returns created user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns a user","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"200":{"description":"Returns updated user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns deleted user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/me":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get authenticated user info","responses":{"200":{"description":"Returns user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/count":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get user count","responses":{"200":{"description":"Returns a number","content":{"application/json":{"schema":{"type":"number"},"example":1}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}}},"components":{"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}},"schemas":{"Error":{"type":"object","required":["error"],"properties":{"data":{"nullable":true,"oneOf":[{"type":"object"},{"type":"array","items":{"type":"object"}}]},"error":{"type":"object","properties":{"status":{"type":"integer"},"name":{"type":"string"},"message":{"type":"string"},"details":{"type":"object"}}}}},"EventRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"price":{"type":"string","pattern":"^\\d*$","example":"123456789"},"description":{"type":"string"},"date":{"type":"string","format":"date-time"},"icon":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"images":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"max_attendees":{"type":"integer"},"duration":{"type":"integer"},"is_active":{"type":"boolean"},"reservations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"EventListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Event"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Event":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"string","pattern":"^\\d*$","example":"123456789"},"description":{"type":"string"},"date":{"type":"string","format":"date-time"},"icon":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"max_attendees":{"type":"integer"},"duration":{"type":"integer"},"is_active":{"type":"boolean"},"reservations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"event":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"string","pattern":"^\\d*$","example":"123456789"},"description":{"type":"string"},"date":{"type":"string","format":"date-time"},"icon":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"max_attendees":{"type":"integer"},"duration":{"type":"integer"},"is_active":{"type":"boolean"},"reservations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"user":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"reservations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"booking_date":{"type":"string","format":"date-time"},"statut":{"type":"string","enum":["pending","confirmed","cancelled"]},"ticket_count":{"type":"integer"},"total_price":{"type":"integer"},"payment_reference":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"EventResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Event"},"meta":{"type":"object"}}},"ReservationRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"event":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"user":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"booking_date":{"type":"string","format":"date-time"},"statut":{"type":"string","enum":["pending","confirmed","cancelled"]},"ticket_count":{"type":"integer"},"total_price":{"type":"integer"},"payment_reference":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ReservationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Reservation"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Reservation":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"event":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"string","pattern":"^\\d*$","example":"123456789"},"description":{"type":"string"},"date":{"type":"string","format":"date-time"},"icon":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"max_attendees":{"type":"integer"},"duration":{"type":"integer"},"is_active":{"type":"boolean"},"reservations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"event":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"user":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"reservations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"booking_date":{"type":"string","format":"date-time"},"statut":{"type":"string","enum":["pending","confirmed","cancelled"]},"ticket_count":{"type":"integer"},"total_price":{"type":"integer"},"payment_reference":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"user":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"booking_date":{"type":"string","format":"date-time"},"statut":{"type":"string","enum":["pending","confirmed","cancelled"]},"ticket_count":{"type":"integer"},"total_price":{"type":"integer"},"payment_reference":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ReservationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Reservation"},"meta":{"type":"object"}}},"UploadFile":{"properties":{"id":{"type":"number"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"number","format":"integer"},"height":{"type":"number","format":"integer"},"formats":{"type":"number"},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"double"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{"type":"object"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-Role":{"type":"object","properties":{"id":{"type":"number"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-User":{"type":"object","properties":{"id":{"type":"number","example":1},"username":{"type":"string","example":"foo.bar"},"email":{"type":"string","example":"<EMAIL>"},"provider":{"type":"string","example":"local"},"confirmed":{"type":"boolean","example":true},"blocked":{"type":"boolean","example":false},"createdAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.258Z"},"updatedAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.267Z"}}},"Users-Permissions-UserRegistration":{"type":"object","properties":{"jwt":{"type":"string","example":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"},"user":{"$ref":"#/components/schemas/Users-Permissions-User"}}},"Users-Permissions-PermissionsTree":{"type":"object","additionalProperties":{"type":"object","description":"every api","properties":{"controllers":{"description":"every controller of the api","type":"object","additionalProperties":{"type":"object","additionalProperties":{"description":"every action of every controller","type":"object","properties":{"enabled":{"type":"boolean"},"policy":{"type":"string"}}}}}}}}},"requestBodies":{"Users-Permissions-RoleRequest":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"name":"foo","description":"role foo","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}}},"tags":[{"name":"Users-Permissions - Auth","description":"Authentication endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}},{"name":"Users-Permissions - Users & Roles","description":"Users, roles, and permissions endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]},
          dom_id: '#swagger-ui',
          docExpansion: "none",
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset,
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl,
          ],
          layout: "StandaloneLayout",
        });

        window.ui = ui;
      }
    </script>

    <script src="/plugins/documentation/swagger-ui-bundle.js"></script>
    <script src="/plugins/documentation/swagger-ui-standalone-preset.js"></script>
  </body>
</html>
