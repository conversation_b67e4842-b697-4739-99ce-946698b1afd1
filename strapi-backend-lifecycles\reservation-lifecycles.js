'use strict';

/**
 * Lifecycle callbacks for the `reservation` model.
 */

module.exports = {
  /**
   * Send confirmation email after reservation is created
   */
  async afterCreate(event) {
    const { result } = event;
    
    try {
      console.log('Reservation created, sending confirmation email...', result.id);
      
      // Get the full reservation data with populated relations
      const reservation = await strapi.documents('api::reservation.reservation').findOne({
        documentId: result.documentId,
        populate: {
          user: true,
          event: true,
        },
      });

      if (!reservation) {
        console.error('Reservation not found for email sending');
        return;
      }

      // Check if user and event data are available
      if (!reservation.user || !reservation.event) {
        console.error('Missing user or event data for reservation email');
        return;
      }

      // Get user email - check if user has email field
      const userEmail = reservation.user.email;
      if (!userEmail) {
        console.error('User email not found for reservation:', reservation.id);
        return;
      }

      // Format event date and time
      const eventDate = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : 'TBD';

      const eventTime = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : 'TBD';

      // Prepare email template data
      const emailData = {
        customerName: reservation.user.username || reservation.user.firstName || 'Valued Customer',
        reservationId: reservation.documentId,
        eventName: reservation.event.name || 'Event',
        eventDate: eventDate,
        eventTime: eventTime,
        numberOfGuests: reservation.ticket_count || 1,
        totalPrice: reservation.total_price || 0,
      };

      // Get email template
      const emailTemplateService = strapi.service('api::email.email-templates');
      const emailTemplate = emailTemplateService.getReservationConfirmationTemplate(emailData);

      // Send email
      await strapi.plugins['email'].services.email.send({
        to: userEmail,
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: emailTemplate.subject,
        text: emailTemplate.text,
        html: emailTemplate.html,
      });

      console.log(`Reservation confirmation email sent to: ${userEmail}`);

    } catch (error) {
      console.error('Error sending reservation confirmation email:', error);
      // Don't throw error to avoid breaking the reservation creation process
    }
  },

  /**
   * Send payment confirmation email when reservation status changes to 'confirmed'
   */
  async afterUpdate(event) {
    const { result, params } = event;
    
    try {
      // Check if status was updated to 'confirmed'
      const updatedData = params.data;
      if (updatedData.statutR !== 'confirmed') {
        return; // Only send email when status becomes 'confirmed'
      }

      console.log('Reservation confirmed, sending payment success email...', result.id);
      
      // Get the full reservation data with populated relations
      const reservation = await strapi.documents('api::reservation.reservation').findOne({
        documentId: result.documentId,
        populate: {
          user: true,
          event: true,
        },
      });

      if (!reservation) {
        console.error('Reservation not found for payment email sending');
        return;
      }

      // Check if user and event data are available
      if (!reservation.user || !reservation.event) {
        console.error('Missing user or event data for payment email');
        return;
      }

      // Get user email
      const userEmail = reservation.user.email;
      if (!userEmail) {
        console.error('User email not found for payment confirmation:', reservation.id);
        return;
      }

      // Format event date and time
      const eventDate = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : 'TBD';

      const eventTime = reservation.event.date ? 
        new Date(reservation.event.date).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : 'TBD';

      // Prepare email template data
      const emailData = {
        customerName: reservation.user.username || reservation.user.firstName || 'Valued Customer',
        reservationId: reservation.documentId,
        eventName: reservation.event.name || 'Event',
        eventDate: eventDate,
        eventTime: eventTime,
        numberOfGuests: reservation.ticket_count || 1,
        totalPrice: reservation.total_price || 0,
        paymentReference: reservation.payment_reference,
      };

      // Get email template
      const emailTemplateService = strapi.service('api::email.email-templates');
      const emailTemplate = emailTemplateService.getPaymentSuccessTemplate(emailData);

      // Send email
      await strapi.plugins['email'].services.email.send({
        to: userEmail,
        from: process.env.EMAIL_DEFAULT_FROM || '<EMAIL>',
        subject: emailTemplate.subject,
        text: emailTemplate.text,
        html: emailTemplate.html,
      });

      console.log(`Payment confirmation email sent to: ${userEmail}`);

    } catch (error) {
      console.error('Error sending payment confirmation email:', error);
      // Don't throw error to avoid breaking the reservation update process
    }
  },
};
