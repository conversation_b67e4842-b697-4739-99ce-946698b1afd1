'use strict';

/**
 * Email templates service
 */

module.exports = () => ({
  /**
   * Generate reservation confirmation email template
   */
  getReservationConfirmationTemplate(data) {
    const {
      customerName,
      reservationId,
      eventName,
      eventDate,
      eventTime,
      numberOfGuests,
      totalPrice,
      hotelName = process.env.HOTEL_NAME || 'Hotel Booking',
      hotelAddress = process.env.HOTEL_ADDRESS || '',
      hotelPhone = process.env.HOTEL_PHONE || '',
      hotelEmail = process.env.HOTEL_EMAIL || '',
      hotelWebsite = process.env.HOTEL_WEBSITE || '',
    } = data;

    const subject = `Reservation Confirmation - ${eventName}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reservation Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .reservation-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { background-color: #34495e; color: white; padding: 15px; text-align: center; font-size: 12px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .highlight { color: #e74c3c; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${hotelName}</h1>
            <h2>Reservation Confirmation</h2>
          </div>
          
          <div class="content">
            <p>Dear ${customerName},</p>
            
            <p>Thank you for your reservation! We're excited to confirm your booking details:</p>
            
            <div class="reservation-details">
              <h3>Reservation Details</h3>
              <p><strong>Reservation ID:</strong> ${reservationId}</p>
              <p><strong>Event:</strong> ${eventName}</p>
              <p><strong>Date:</strong> ${eventDate}</p>
              <p><strong>Time:</strong> ${eventTime}</p>
              <p><strong>Number of Guests:</strong> ${numberOfGuests}</p>
              <p><strong>Total Amount:</strong> <span class="highlight">€${totalPrice}</span></p>
              <p><strong>Status:</strong> <span class="highlight">Pending Payment</span></p>
            </div>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
              <li>Complete your payment to confirm your reservation</li>
              <li>You will receive a payment confirmation email once processed</li>
              <li>Arrive 15 minutes before the event time</li>
            </ul>
            
            <p>If you have any questions or need to make changes to your reservation, please contact us immediately.</p>
          </div>
          
          <div class="footer">
            <p><strong>${hotelName}</strong></p>
            <p>${hotelAddress}</p>
            <p>Phone: ${hotelPhone} | Email: ${hotelEmail}</p>
            <p>Website: ${hotelWebsite}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Dear ${customerName},

      Thank you for your reservation! We're excited to confirm your booking details:

      Reservation Details:
      - Reservation ID: ${reservationId}
      - Event: ${eventName}
      - Date: ${eventDate}
      - Time: ${eventTime}
      - Number of Guests: ${numberOfGuests}
      - Total Amount: €${totalPrice}
      - Status: Pending Payment

      Next Steps:
      - Complete your payment to confirm your reservation
      - You will receive a payment confirmation email once processed
      - Arrive 15 minutes before the event time

      If you have any questions or need to make changes to your reservation, please contact us immediately.

      Best regards,
      ${hotelName}
      ${hotelAddress}
      Phone: ${hotelPhone}
      Email: ${hotelEmail}
      Website: ${hotelWebsite}
    `;

    return { subject, html, text };
  },

  /**
   * Generate payment success email template
   */
  getPaymentSuccessTemplate(data) {
    const {
      customerName,
      reservationId,
      eventName,
      eventDate,
      eventTime,
      numberOfGuests,
      totalPrice,
      paymentReference,
      hotelName = process.env.HOTEL_NAME || 'Hotel Booking',
      hotelAddress = process.env.HOTEL_ADDRESS || '',
      hotelPhone = process.env.HOTEL_PHONE || '',
      hotelEmail = process.env.HOTEL_EMAIL || '',
      hotelWebsite = process.env.HOTEL_WEBSITE || '',
    } = data;

    const subject = `Payment Confirmed - ${eventName} Reservation`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #27ae60; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .reservation-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { background-color: #34495e; color: white; padding: 15px; text-align: center; font-size: 12px; }
          .success { color: #27ae60; font-weight: bold; }
          .highlight { color: #e74c3c; font-weight: bold; }
          .check-mark { font-size: 48px; color: #27ae60; text-align: center; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${hotelName}</h1>
            <h2>Payment Confirmed!</h2>
          </div>
          
          <div class="content">
            <div class="check-mark">✓</div>
            
            <p>Dear ${customerName},</p>
            
            <p class="success">Great news! Your payment has been successfully processed and your reservation is now confirmed.</p>
            
            <div class="reservation-details">
              <h3>Confirmed Reservation Details</h3>
              <p><strong>Reservation ID:</strong> ${reservationId}</p>
              <p><strong>Event:</strong> ${eventName}</p>
              <p><strong>Date:</strong> ${eventDate}</p>
              <p><strong>Time:</strong> ${eventTime}</p>
              <p><strong>Number of Guests:</strong> ${numberOfGuests}</p>
              <p><strong>Total Paid:</strong> <span class="highlight">€${totalPrice}</span></p>
              <p><strong>Payment Reference:</strong> ${paymentReference || 'N/A'}</p>
              <p><strong>Status:</strong> <span class="success">CONFIRMED</span></p>
            </div>
            
            <p><strong>What to expect:</strong></p>
            <ul>
              <li>Please arrive 15 minutes before the event time</li>
              <li>Bring a valid ID for verification</li>
              <li>Your table will be reserved under the name: ${customerName}</li>
              <li>Contact us if you need any special accommodations</li>
            </ul>
            
            <p>We look forward to welcoming you to ${hotelName}!</p>
            
            <p><em>Please save this email as your booking confirmation.</em></p>
          </div>
          
          <div class="footer">
            <p><strong>${hotelName}</strong></p>
            <p>${hotelAddress}</p>
            <p>Phone: ${hotelPhone} | Email: ${hotelEmail}</p>
            <p>Website: ${hotelWebsite}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Dear ${customerName},

      Great news! Your payment has been successfully processed and your reservation is now confirmed.

      Confirmed Reservation Details:
      - Reservation ID: ${reservationId}
      - Event: ${eventName}
      - Date: ${eventDate}
      - Time: ${eventTime}
      - Number of Guests: ${numberOfGuests}
      - Total Paid: €${totalPrice}
      - Payment Reference: ${paymentReference || 'N/A'}
      - Status: CONFIRMED

      What to expect:
      - Please arrive 15 minutes before the event time
      - Bring a valid ID for verification
      - Your table will be reserved under the name: ${customerName}
      - Contact us if you need any special accommodations

      We look forward to welcoming you to ${hotelName}!

      Please save this email as your booking confirmation.

      Best regards,
      ${hotelName}
      ${hotelAddress}
      Phone: ${hotelPhone}
      Email: ${hotelEmail}
      Website: ${hotelWebsite}
    `;

    return { subject, html, text };
  },
});
